import { test, expect } from '@playwright/test';

/**
 * Test the comprehensive SSE fix with polling fallback
 */

test.describe('SSE Fix with Polling Fallback', () => {
  test('should complete reanalysis with improved SSE or polling fallback', async ({ page }) => {
    test.setTimeout(120000); // 2 minutes
    
    console.log('🧪 Testing SSE fix with polling fallback');

    // Navigate to article
    await page.goto('/article/5');
    await page.waitForLoadState('load');
    console.log('✅ Page loaded');

    // Click reanalysis button
    const reanalyzeBtn = page.locator('#reanalyze-btn');
    await expect(reanalyzeBtn).toBeVisible();
    
    console.log('🖱️ Clicking reanalysis button');
    await reanalyzeBtn.click();

    // Wait for button to be disabled
    await expect(reanalyzeBtn).toBeDisabled({ timeout: 5000 });
    console.log('✅ Button disabled - reanalysis started');

    // Wait for completion (button re-enabled) with extended timeout
    console.log('⏳ Waiting for reanalysis completion (SSE or polling fallback)...');
    await expect(reanalyzeBtn).toBeEnabled({ timeout: 100000 }); // 100 seconds
    console.log('✅ Button re-enabled - reanalysis completed');

    // Wait for updateBiasAnalysis to run
    await page.waitForTimeout(8000);

    // Check final state
    const finalBiasScore = await page.locator('#bias-score').textContent();
    console.log('📊 Final bias score:', finalBiasScore);

    // Should NOT contain manual refresh message
    expect(finalBiasScore).not.toContain('Analysis complete - refresh page for updated results');
    console.log('✅ NO manual refresh message');

    // Should show either updated score or auto-refresh
    const hasUpdatedScore = finalBiasScore?.includes('Bias Score:');
    const hasAutoRefresh = finalBiasScore?.includes('Auto-refreshing');
    
    if (hasUpdatedScore) {
      console.log('✅ Bias score updated successfully');
      expect(finalBiasScore).toMatch(/Bias Score: -?\d+\.\d{3}/);
    } else if (hasAutoRefresh) {
      console.log('✅ Auto-refresh countdown active');
      // Wait for page refresh
      await page.waitForLoadState('load', { timeout: 10000 });
      console.log('✅ Page auto-refreshed');
    } else {
      console.log('ℹ️ Other state:', finalBiasScore);
    }

    console.log('🎉 SSE fix test completed successfully');
  });
});
