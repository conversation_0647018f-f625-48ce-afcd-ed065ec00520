exclude: '^(node_modules|p|backup)/'
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: "v4.3.0"
    hooks:
      - id: end-of-file-fixer
      - id: trailing-whitespace
      - id: check-yaml
      - id: check-added-large-files
  - repo: local
    hooks:
      - id: go-mod-tidy
        name: go mod tidy
        entry: go mod tidy
        language: system
        files: "^go\\.mod$"
        pass_filenames: false
      - id: go-fmt
        name: go fmt
        entry: go fmt
        language: system
        files: "^(cmd|internal)/.*\\.go$"
        args: ["./..."]
        pass_filenames: false
      - id: go-vet
        name: go vet
        entry: go vet
        language: system
        files: "^(cmd|internal)/.*\\.go$"
        args: ["./..."]
        pass_filenames: false
      - id: golangci-lint
        name: golangci-lint
        entry: C:\Users\<USER>\go\bin\golangci-lint.exe
        language: system
        files: "^(cmd|internal)/.*\\.go$"
        args: ["run", "--timeout=5m"]
        pass_filenames: false
      - id: go-unit-tests
        name: go unit tests
        entry: make unit ENABLE_RACE_DETECTION=false
        language: system
        files: "^(cmd|internal)/.*\\.go$"
        pass_filenames: false
        require_serial: true
