<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Article.Title}} - NewsBalancer</title>

    <!-- Unified CSS System -->
    <link rel="stylesheet" href="/static/css/app-consolidated.css?v=1" />


</head>
<body>
    <header class="navbar">
        <div class="container">
            <a href="/articles" class="navbar-brand">NewsBalancer</a>
            <nav class="navbar-nav">
                <a href="/articles">Articles</a>
                <a href="/admin">Admin</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="two-column-layout">
            <div>
                <h1>{{.Article.Title}}</h1>
                <div class="article-meta">
                    <div><strong>Source:</strong> {{.Article.Source}}</div>
                    <div><strong>Published:</strong> {{.Article.PubDate.Format "2006-01-02 15:04"}}</div>
                    <div><strong>Bias Score:</strong> {{if .Article.CompositeScore}}{{.Article.CompositeScore}}{{else}}N/A{{end}} (confidence: {{if .Article.Confidence}}{{.Article.Confidence}}{{else}}N/A{{end}}%)</div>
                </div>

                {{if .Article.Summary}}
                <div class="article-summary">
                    <strong>Summary:</strong> {{.Article.Summary}}
                </div>
                {{end}}
                
                <div class="bias-analysis">
                    <h2>Bias Analysis</h2>
                    <div class="bias-score" id="bias-score">Bias Analysis</div>                    <div id="bias-label-container">
                        <!-- Bias label section - currently not available in database -->
                        <div class="bias-label bias-unknown">Bias analysis pending</div>
                    </div>                      <div class="analysis-details" id="analysis-details">
                        <p>Detailed Confidence: <span id="confidence-value">{{if .Article.Confidence}}{{.Article.Confidence}}{{else}}N/A{{end}}</span>%</p>
                    </div>
                    
                    <!-- Progress Indicator for real-time updates -->
                    <progress-indicator
                        id="reanalysis-progress"
                        article-id="{{.Article.ID}}"
                        auto-connect="false"
                        show-details="true"
                        class="progress-hidden">
                    </progress-indicator>
                    
                    <button class="btn btn-primary" id="reanalyze-btn" data-article-id="{{.Article.ID}}">
                        <span id="btn-text">Request Reanalysis</span>
                        <span id="btn-loading" class="btn-loading-hidden">Processing...</span>
                    </button>
                </div>

                <div class="article-content">
                    {{.Article.Content}}
                </div>
                
                <div class="article-meta">
                    <div>Publication Date: {{.Article.PubDate.Format "2006-01-02 15:04"}}</div>
                    <div>Source: {{.Article.Source}}</div>
                    <div>Bias Score: {{if .Article.CompositeScore}}{{.Article.CompositeScore}}{{else}}N/A{{end}} (confidence: {{if .Article.Confidence}}{{.Article.Confidence}}{{else}}N/A{{end}}%)</div>
                </div>
            </div>

            <div class="sidebar">
                <div class="recent-articles">
                    <h3>Recent Articles</h3>                    {{range .RecentArticles}}
                    <div class="recent-article-item">
                        <a href="/article/{{.ID}}">{{.Title}}</a>
                        <div>
                            <!-- Bias labels not available from database -->
                            <small class="bias-unknown">Analysis pending</small>
                        </div>
                    </div>
                    {{end}}
                </div>

                <div class="stats">
                    <h3>Statistics</h3>
                    <div class="stats-item">
                        <span>Total Articles:</span>
                        <span>{{.Stats.TotalArticles}}</span>
                    </div>
                    <div class="stats-item">
                        <span>Left Leaning:</span>
                        <span>{{.Stats.LeftCount}} ({{.Stats.LeftPercentage}}%)</span>
                    </div>
                    <div class="stats-item">
                        <span>Center:</span>
                        <span>{{.Stats.CenterCount}} ({{.Stats.CenterPercentage}}%)</span>
                    </div>
                    <div class="stats-item">
                        <span>Right Leaning:</span>
                        <span>{{.Stats.RightCount}} ({{.Stats.RightPercentage}}%)</span>
                    </div>
                </div>
            </div>
        </div>
    </div>    <!-- Import ProgressIndicator component and SSEClient utility -->
    <script type="module" src="/static/js/components/ProgressIndicator.js"></script>
    <script type="module" src="/static/js/utils/SSEClient.js"></script>
    
    <script>
        // Enhanced article detail page with real-time SSE progress tracking
        document.addEventListener('DOMContentLoaded', function() {
            const reanalyzeBtn = document.getElementById('reanalyze-btn');
            const btnText = document.getElementById('btn-text');
            const btnLoading = document.getElementById('btn-loading');
            const progressIndicator = document.getElementById('reanalysis-progress');

            // Elements for updating bias analysis results
            const biasScoreElement = document.getElementById('bias-score');
            
            if (reanalyzeBtn) {
                reanalyzeBtn.addEventListener('click', async function() {
                    console.log('🖱️ Reanalyze button clicked!');
                    const articleId = this.getAttribute('data-article-id');
                    console.log('📄 Article ID:', articleId);
                    
                    // SIMPLE SOLUTION: Skip ProgressIndicator entirely and use direct polling
                    console.log('🔌 Starting simple polling-based completion detection');

                    // Start polling for completion immediately
                    let pollAttempts = 0;
                    const maxPollAttempts = 60; // 2 minutes of polling
                    const pollInterval = 2000; // Poll every 2 seconds

                    const pollTimer = setInterval(async () => {
                        pollAttempts++;
                        console.log(`⏳ Polling for completion (attempt ${pollAttempts}/${maxPollAttempts})`);

                        try {
                            // Check if analysis is complete by fetching bias data
                            const response = await fetch(`/api/articles/${articleId}/bias`);
                            if (response.ok) {
                                const biasData = await response.json();

                                if (biasData.success && biasData.data && biasData.data.composite_score !== null) {
                                    console.log('✅ Analysis completed via polling - re-enabling button');

                                    // Clear polling
                                    clearInterval(pollTimer);

                                    // Clear emergency timeout
                                    if (reanalyzeBtn._emergencyTimeout) {
                                        clearTimeout(reanalyzeBtn._emergencyTimeout);
                                        reanalyzeBtn._emergencyTimeout = null;
                                    }

                                    // Reset button state
                                    btnText.style.display = 'inline';
                                    btnLoading.style.display = 'none';
                                    reanalyzeBtn.disabled = false;

                                    // Update bias analysis
                                    await updateBiasAnalysis(articleId);
                                    return;
                                }
                            }
                        } catch (error) {
                            console.error(`❌ Polling attempt ${pollAttempts} failed:`, error);
                        }

                        // Stop polling after max attempts - emergency timeout will handle it
                        if (pollAttempts >= maxPollAttempts) {
                            console.log('⏰ Polling reached max attempts - relying on emergency timeout');
                            clearInterval(pollTimer);
                        }
                    }, pollInterval);

                    // Disable button and show loading state
                    this.disabled = true;
                    btnText.style.display = 'none';
                    btnLoading.style.display = 'inline';
                    
                    try {
                        // Trigger reanalysis
                        const response = await fetch(`/api/llm/reanalyze/${articleId}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        // Set up progress indicator event listeners BEFORE showing it
                        // This prevents race condition where completion event fires before listeners are attached

                        // Remove any existing listeners to prevent duplicates
                        progressIndicator.removeEventListener('completed', progressIndicator._completedHandler);
                        progressIndicator.removeEventListener('error', progressIndicator._errorHandler);
                        progressIndicator.removeEventListener('connectionerror', progressIndicator._connectionErrorHandler);
                        progressIndicator.removeEventListener('autohide', progressIndicator._autoHideHandler);

                        // Define and store event handlers for cleanup
                        progressIndicator._completedHandler = async (event) => {
                            console.log('Analysis completed:', event.detail);

                            // Clear emergency timeout since completion was received
                            if (reanalyzeBtn._emergencyTimeout) {
                                clearTimeout(reanalyzeBtn._emergencyTimeout);
                                reanalyzeBtn._emergencyTimeout = null;
                                console.log('Emergency timeout cleared - normal completion received');
                            }

                            // Don't hide progress indicator immediately - let users see completion state
                            // The ProgressIndicator will auto-hide after a delay via the 'autohide' event

                            // Reset button state
                            btnText.style.display = 'inline';
                            btnLoading.style.display = 'none';
                            reanalyzeBtn.disabled = false;

                            // Fetch updated bias data after a delay to allow users to see completion
                            setTimeout(async () => {
                                await updateBiasAnalysis(articleId);
                            }, 4000); // 4 second delay to allow users to see the completion state
                        };

                        progressIndicator._errorHandler = (event) => {
                            console.error('Analysis error:', event.detail);

                            // Hide progress indicator
                            progressIndicator.style.display = 'none';

                            // Reset button state with error message
                            btnText.textContent = 'Error - Try Again';
                            btnText.style.display = 'inline';
                            btnLoading.style.display = 'none';
                            reanalyzeBtn.disabled = false;

                            // Reset button text after a few seconds
                            setTimeout(() => {
                                btnText.textContent = 'Request Reanalysis';
                            }, 3000);
                        };

                        progressIndicator._connectionErrorHandler = (event) => {
                            console.error('Connection error:', event.detail);

                            // Don't hide progress indicator if analysis completed successfully
                            // Check if the progress indicator shows completion
                            const isCompleted = progressIndicator._status === 'completed' ||
                                              progressIndicator._progressValue >= 100;

                            if (!isCompleted) {
                                // Hide progress indicator only if analysis didn't complete
                                progressIndicator.style.display = 'none';

                                // Reset button state
                                btnText.textContent = 'Connection Error - Try Again';
                                btnText.style.display = 'inline';
                                btnLoading.style.display = 'none';
                                reanalyzeBtn.disabled = false;

                                // Reset button text after a few seconds
                                setTimeout(() => {
                                    btnText.textContent = 'Request Reanalysis';
                                }, 3000);
                            } else {
                                console.log('Connection closed after completion - keeping progress visible');
                            }
                        };

                        // Add autohide handler for delayed hiding after completion
                        progressIndicator._autoHideHandler = (event) => {
                            console.log('Auto-hiding progress indicator after completion:', event.detail);
                            progressIndicator.style.display = 'none';
                            progressIndicator.classList.add('progress-hidden');
                        };

                        // Add the event listeners
                        progressIndicator.addEventListener('completed', progressIndicator._completedHandler);
                        progressIndicator.addEventListener('error', progressIndicator._errorHandler);
                        progressIndicator.addEventListener('connectionerror', progressIndicator._connectionErrorHandler);
                        progressIndicator.addEventListener('autohide', progressIndicator._autoHideHandler);

                        // Show progress indicator AFTER event listeners are set up
                        // This prevents race condition with auto-connect="true"
                        progressIndicator.classList.remove('progress-hidden');
                        progressIndicator.style.display = 'block';

                        // EMERGENCY TIMEOUT: Force button re-enable after 2 minutes if nothing happens
                        // This ensures the UI never gets permanently stuck
                        const emergencyTimeout = setTimeout(() => {
                            console.warn('EMERGENCY TIMEOUT: Force re-enabling button after 2 minutes');

                            // Reset button state
                            btnText.style.display = 'inline';
                            btnLoading.style.display = 'none';
                            reanalyzeBtn.disabled = false;

                            // Hide progress indicator
                            progressIndicator.style.display = 'none';
                            progressIndicator.classList.add('progress-hidden');

                            // Trigger auto-refresh to show updated results
                            updateBiasAnalysis(articleId);
                        }, 120000); // 2 minutes

                        // Store timeout ID so it can be cleared if completion happens normally
                        reanalyzeBtn._emergencyTimeout = emergencyTimeout;

                    } catch (error) {
                        console.error('Error requesting reanalysis:', error);
                        
                        // Hide progress indicator
                        progressIndicator.style.display = 'none';
                        
                        // Reset button state with error message
                        btnText.textContent = 'Request Failed - Try Again';
                        btnText.style.display = 'inline';
                        btnLoading.style.display = 'none';
                        reanalyzeBtn.disabled = false;
                        
                        // Reset button text after a few seconds
                        setTimeout(() => {
                            btnText.textContent = 'Request Reanalysis';
                        }, 3000);
                    }
                });
            }
            
            // Function to update bias analysis UI with fresh data
            async function updateBiasAnalysis(articleId) {
                try {
                    const response = await fetch(`/api/articles/${articleId}/bias`);
                    if (!response.ok) {
                        throw new Error(`Failed to fetch bias data: ${response.statusText}`);
                    }
                    
                    const biasData = await response.json();
                      // Update bias score with the composite score
                    if (biasScoreElement && biasData.composite_score !== null) {
                        biasScoreElement.textContent = `Bias Score: ${biasData.composite_score.toFixed(3)}`;
                    } else if (biasScoreElement) {
                        biasScoreElement.textContent = 'Bias Score: Analysis pending';
                    }
                      console.log('Bias analysis updated with fresh data');
                    
                } catch (error) {
                    console.error('Error updating bias analysis:', error);
                    
                    // Auto-refresh instead of manual refresh message
                    if (biasScoreElement) {
                        biasScoreElement.textContent = 'Auto-refreshing page in 3 seconds...';
                        biasScoreElement.style.fontStyle = 'italic';
                        biasScoreElement.style.color = '#007bff';

                        setTimeout(() => {
                            console.log('Auto-refreshing page to show updated results');
                            window.location.reload();
                        }, 3000);
                    }
                }
            }
        });
    </script>
</body>
</html>
