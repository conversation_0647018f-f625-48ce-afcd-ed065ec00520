<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Article.Title}} - NewsBalancer</title>

    <!-- Unified CSS System -->
    <link rel="stylesheet" href="/static/css/app-consolidated.css?v=1" />


</head>
<body>
    <header class="navbar">
        <div class="container">
            <a href="/articles" class="navbar-brand">NewsBalancer</a>
            <nav class="navbar-nav">
                <a href="/articles">Articles</a>
                <a href="/admin">Admin</a>
            </nav>
        </div>
    </header>

    <div class="container">
        <div class="two-column-layout">
            <div>
                <h1>{{.Article.Title}}</h1>
                <div class="article-meta">
                    <div><strong>Source:</strong> {{.Article.Source}}</div>
                    <div><strong>Published:</strong> {{.Article.PubDate.Format "2006-01-02 15:04"}}</div>
                    <div><strong>Bias Score:</strong> {{if .Article.CompositeScore}}{{.Article.CompositeScore}}{{else}}N/A{{end}} (confidence: {{if .Article.Confidence}}{{.Article.Confidence}}{{else}}N/A{{end}}%)</div>
                </div>

                {{if .Article.Summary}}
                <div class="article-summary">
                    <strong>Summary:</strong> {{.Article.Summary}}
                </div>
                {{end}}
                
                <div class="bias-analysis">
                    <h2>Bias Analysis</h2>
                    <div class="bias-score" id="bias-score">Bias Analysis</div>                    <div id="bias-label-container">
                        <!-- Bias label section - currently not available in database -->
                        <div class="bias-label bias-unknown">Bias analysis pending</div>
                    </div>                      <div class="analysis-details" id="analysis-details">
                        <p>Detailed Confidence: <span id="confidence-value">{{if .Article.Confidence}}{{.Article.Confidence}}{{else}}N/A{{end}}</span>%</p>
                    </div>
                    
                    <!-- Progress Indicator for real-time updates -->
                    <progress-indicator
                        id="reanalysis-progress"
                        article-id="{{.Article.ID}}"
                        auto-connect="false"
                        show-details="true"
                        class="progress-hidden">
                    </progress-indicator>
                    
                    <button class="btn btn-primary" id="reanalyze-btn" data-article-id="{{.Article.ID}}">
                        <span id="btn-text">Request Reanalysis</span>
                        <span id="btn-loading" class="btn-loading-hidden">Processing...</span>
                    </button>
                </div>

                <div class="article-content">
                    {{.Article.Content}}
                </div>
                
                <div class="article-meta">
                    <div>Publication Date: {{.Article.PubDate.Format "2006-01-02 15:04"}}</div>
                    <div>Source: {{.Article.Source}}</div>
                    <div>Bias Score: {{if .Article.CompositeScore}}{{.Article.CompositeScore}}{{else}}N/A{{end}} (confidence: {{if .Article.Confidence}}{{.Article.Confidence}}{{else}}N/A{{end}}%)</div>
                </div>
            </div>

            <div class="sidebar">
                <div class="recent-articles">
                    <h3>Recent Articles</h3>                    {{range .RecentArticles}}
                    <div class="recent-article-item">
                        <a href="/article/{{.ID}}">{{.Title}}</a>
                        <div>
                            <!-- Bias labels not available from database -->
                            <small class="bias-unknown">Analysis pending</small>
                        </div>
                    </div>
                    {{end}}
                </div>

                <div class="stats">
                    <h3>Statistics</h3>
                    <div class="stats-item">
                        <span>Total Articles:</span>
                        <span>{{.Stats.TotalArticles}}</span>
                    </div>
                    <div class="stats-item">
                        <span>Left Leaning:</span>
                        <span>{{.Stats.LeftCount}} ({{.Stats.LeftPercentage}}%)</span>
                    </div>
                    <div class="stats-item">
                        <span>Center:</span>
                        <span>{{.Stats.CenterCount}} ({{.Stats.CenterPercentage}}%)</span>
                    </div>
                    <div class="stats-item">
                        <span>Right Leaning:</span>
                        <span>{{.Stats.RightCount}} ({{.Stats.RightPercentage}}%)</span>
                    </div>
                </div>
            </div>
        </div>
    </div>    <!-- Import ProgressIndicator component and SSEClient utility -->
    <script type="module" src="/static/js/components/ProgressIndicator.js"></script>
    <script type="module" src="/static/js/utils/SSEClient.js"></script>
    
    <script>
        // Enhanced article detail page with real-time SSE progress tracking
        document.addEventListener('DOMContentLoaded', function() {
            const reanalyzeBtn = document.getElementById('reanalyze-btn');
            const btnText = document.getElementById('btn-text');
            const btnLoading = document.getElementById('btn-loading');
            const progressIndicator = document.getElementById('reanalysis-progress');

            // Elements for updating bias analysis results
            const biasScoreElement = document.getElementById('bias-score');
            
            if (reanalyzeBtn) {
                reanalyzeBtn.addEventListener('click', async function() {
                    console.log('🖱️ Reanalyze button clicked!');
                    const articleId = this.getAttribute('data-article-id');
                    console.log('📄 Article ID:', articleId);
                    
                    // Reset progress indicator to ensure clean state
                    if (progressIndicator && typeof progressIndicator.reset === 'function') {
                        progressIndicator.reset();
                    } else {
                        console.warn('ProgressIndicator reset method not available');
                    }

                    // Connect to SSE for progress updates
                    if (progressIndicator && typeof progressIndicator.connect === 'function') {
                        console.log('🔌 Connecting ProgressIndicator to SSE');
                        progressIndicator.connect(articleId);
                    } else {
                        console.warn('ProgressIndicator connect method not available');
                    }

                    // Disable button and show loading state
                    this.disabled = true;
                    btnText.style.display = 'none';
                    btnLoading.style.display = 'inline';
                    
                    try {
                        // Trigger reanalysis
                        const response = await fetch(`/api/llm/reanalyze/${articleId}`, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json'
                            }
                        });
                        
                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        // Set up progress indicator event listeners BEFORE showing it
                        // This prevents race condition where completion event fires before listeners are attached

                        // Remove any existing listeners to prevent duplicates
                        progressIndicator.removeEventListener('completed', progressIndicator._completedHandler);
                        progressIndicator.removeEventListener('error', progressIndicator._errorHandler);
                        progressIndicator.removeEventListener('connectionerror', progressIndicator._connectionErrorHandler);
                        progressIndicator.removeEventListener('autohide', progressIndicator._autoHideHandler);

                        // Define and store event handlers for cleanup
                        progressIndicator._completedHandler = async (event) => {
                            console.log('Analysis completed via SSE:', event.detail);

                            // Clear any polling fallback since SSE worked
                            if (progressIndicator._pollingInterval) {
                                clearInterval(progressIndicator._pollingInterval);
                                progressIndicator._pollingInterval = null;
                                console.log('Cleared polling fallback - SSE completion received');
                            }

                            // Clear timeout fallback since SSE worked
                            if (progressIndicator._timeoutFallback) {
                                clearTimeout(progressIndicator._timeoutFallback);
                                progressIndicator._timeoutFallback = null;
                                console.log('Cleared timeout fallback - SSE completion received');
                            }

                            // Don't hide progress indicator immediately - let users see completion state
                            // The ProgressIndicator will auto-hide after a delay via the 'autohide' event

                            // Reset button state
                            btnText.style.display = 'inline';
                            btnLoading.style.display = 'none';
                            reanalyzeBtn.disabled = false;

                            // Fetch updated bias data after a delay to allow users to see completion
                            setTimeout(async () => {
                                await updateBiasAnalysis(articleId);
                            }, 4000); // 4 second delay to allow users to see the completion state
                        };

                        progressIndicator._errorHandler = (event) => {
                            console.error('Analysis error:', event.detail);

                            // Clear fallback mechanisms
                            if (progressIndicator._pollingInterval) {
                                clearInterval(progressIndicator._pollingInterval);
                                progressIndicator._pollingInterval = null;
                            }
                            if (progressIndicator._timeoutFallback) {
                                clearTimeout(progressIndicator._timeoutFallback);
                                progressIndicator._timeoutFallback = null;
                            }

                            // Hide progress indicator
                            progressIndicator.style.display = 'none';

                            // Reset button state with error message
                            btnText.textContent = 'Error - Try Again';
                            btnText.style.display = 'inline';
                            btnLoading.style.display = 'none';
                            reanalyzeBtn.disabled = false;

                            // Reset button text after a few seconds
                            setTimeout(() => {
                                btnText.textContent = 'Request Reanalysis';
                            }, 3000);
                        };

                        progressIndicator._connectionErrorHandler = (event) => {
                            console.error('Connection error:', event.detail);

                            // Don't hide progress indicator if analysis completed successfully
                            // Check if the progress indicator shows completion
                            const isCompleted = progressIndicator._status === 'completed' ||
                                              progressIndicator._progressValue >= 100;

                            if (!isCompleted) {
                                // Don't clear fallback mechanisms here - let them continue
                                // The polling fallback will handle completion detection
                                console.log('SSE connection failed - relying on polling fallback');

                                // Only reset button if polling also fails (handled by timeout fallback)
                            } else {
                                console.log('Connection closed after completion - keeping progress visible');

                                // Clear fallback mechanisms since analysis completed
                                if (progressIndicator._pollingInterval) {
                                    clearInterval(progressIndicator._pollingInterval);
                                    progressIndicator._pollingInterval = null;
                                }
                                if (progressIndicator._timeoutFallback) {
                                    clearTimeout(progressIndicator._timeoutFallback);
                                    progressIndicator._timeoutFallback = null;
                                }
                            }
                        };

                        // Add autohide handler for delayed hiding after completion
                        progressIndicator._autoHideHandler = (event) => {
                            console.log('Auto-hiding progress indicator after completion:', event.detail);
                            progressIndicator.style.display = 'none';
                            progressIndicator.classList.add('progress-hidden');
                        };

                        // Add the event listeners
                        progressIndicator.addEventListener('completed', progressIndicator._completedHandler);
                        progressIndicator.addEventListener('error', progressIndicator._errorHandler);
                        progressIndicator.addEventListener('connectionerror', progressIndicator._connectionErrorHandler);
                        progressIndicator.addEventListener('autohide', progressIndicator._autoHideHandler);

                        // Show progress indicator AFTER event listeners are set up
                        // This prevents race condition with auto-connect="true"
                        progressIndicator.classList.remove('progress-hidden');
                        progressIndicator.style.display = 'block';

                        // FALLBACK MECHANISM: Start polling for completion in case SSE fails
                        // This ensures auto-refresh works even if SSE connection has issues
                        console.log('Starting polling fallback for reanalysis completion');

                        let pollAttempts = 0;
                        const maxPollAttempts = 30; // 30 attempts = 60 seconds max
                        const pollInterval = 2000; // Poll every 2 seconds

                        progressIndicator._pollingInterval = setInterval(async () => {
                            pollAttempts++;
                            console.log(`Polling attempt ${pollAttempts}/${maxPollAttempts} for completion`);

                            try {
                                const response = await fetch(`/api/llm/score-progress/${articleId}`);
                                if (response.ok) {
                                    const progressData = await response.json();
                                    console.log('Polling response:', progressData);

                                    // Check if analysis is complete
                                    if (progressData.status === 'Complete' ||
                                        progressData.status === 'Done' ||
                                        progressData.percent >= 100 ||
                                        progressData.progress >= 100) {

                                        console.log('Analysis completed via polling fallback');

                                        // Clear polling
                                        clearInterval(progressIndicator._pollingInterval);
                                        progressIndicator._pollingInterval = null;

                                        // Clear timeout fallback
                                        if (progressIndicator._timeoutFallback) {
                                            clearTimeout(progressIndicator._timeoutFallback);
                                            progressIndicator._timeoutFallback = null;
                                        }

                                        // Reset button state
                                        btnText.style.display = 'inline';
                                        btnLoading.style.display = 'none';
                                        reanalyzeBtn.disabled = false;

                                        // Hide progress indicator
                                        progressIndicator.style.display = 'none';
                                        progressIndicator.classList.add('progress-hidden');

                                        // Trigger auto-refresh logic
                                        console.log('Triggering auto-refresh via polling fallback');
                                        setTimeout(async () => {
                                            await updateBiasAnalysis(articleId);
                                        }, 2000); // 2 second delay

                                        return;
                                    }

                                    // Check for error status
                                    if (progressData.status === 'Error' || progressData.status === 'Failed') {
                                        console.log('Analysis failed via polling:', progressData.message);

                                        // Clear polling
                                        clearInterval(progressIndicator._pollingInterval);
                                        progressIndicator._pollingInterval = null;

                                        // Clear timeout fallback
                                        if (progressIndicator._timeoutFallback) {
                                            clearTimeout(progressIndicator._timeoutFallback);
                                            progressIndicator._timeoutFallback = null;
                                        }

                                        // Reset button state with error
                                        btnText.textContent = 'Analysis Failed - Try Again';
                                        btnText.style.display = 'inline';
                                        btnLoading.style.display = 'none';
                                        reanalyzeBtn.disabled = false;

                                        // Hide progress indicator
                                        progressIndicator.style.display = 'none';
                                        progressIndicator.classList.add('progress-hidden');

                                        // Reset button text after delay
                                        setTimeout(() => {
                                            btnText.textContent = 'Request Reanalysis';
                                        }, 3000);

                                        return;
                                    }
                                }
                            } catch (error) {
                                console.error(`Polling attempt ${pollAttempts} failed:`, error);
                            }

                            // Stop polling after max attempts
                            if (pollAttempts >= maxPollAttempts) {
                                console.log('Polling fallback reached max attempts - triggering timeout fallback');
                                clearInterval(progressIndicator._pollingInterval);
                                progressIndicator._pollingInterval = null;

                                // Trigger timeout fallback
                                if (progressIndicator._timeoutFallback) {
                                    // Let timeout fallback handle it
                                } else {
                                    // Fallback to auto-refresh anyway
                                    console.log('Triggering auto-refresh after polling timeout');
                                    btnText.style.display = 'inline';
                                    btnLoading.style.display = 'none';
                                    reanalyzeBtn.disabled = false;
                                    progressIndicator.style.display = 'none';
                                    progressIndicator.classList.add('progress-hidden');

                                    setTimeout(async () => {
                                        await updateBiasAnalysis(articleId);
                                    }, 1000);
                                }
                            }
                        }, pollInterval);

                        // TIMEOUT FALLBACK: Final safety net after 90 seconds
                        progressIndicator._timeoutFallback = setTimeout(async () => {
                            console.log('Timeout fallback triggered - forcing auto-refresh');

                            // Clear polling if still running
                            if (progressIndicator._pollingInterval) {
                                clearInterval(progressIndicator._pollingInterval);
                                progressIndicator._pollingInterval = null;
                            }

                            // Reset button state
                            btnText.style.display = 'inline';
                            btnLoading.style.display = 'none';
                            reanalyzeBtn.disabled = false;

                            // Hide progress indicator
                            progressIndicator.style.display = 'none';
                            progressIndicator.classList.add('progress-hidden');

                            // Force auto-refresh
                            console.log('Forcing auto-refresh after timeout');
                            await updateBiasAnalysis(articleId);
                        }, 90000); // 90 second timeout
                        
                    } catch (error) {
                        console.error('Error requesting reanalysis:', error);
                        
                        // Hide progress indicator
                        progressIndicator.style.display = 'none';
                        
                        // Reset button state with error message
                        btnText.textContent = 'Request Failed - Try Again';
                        btnText.style.display = 'inline';
                        btnLoading.style.display = 'none';
                        reanalyzeBtn.disabled = false;
                        
                        // Reset button text after a few seconds
                        setTimeout(() => {
                            btnText.textContent = 'Request Reanalysis';
                        }, 3000);
                    }
                });
            }
            
            // Function to update bias analysis UI with fresh data
            // Implements retry logic with exponential backoff for better reliability
            async function updateBiasAnalysis(articleId, maxRetries = 3, baseDelay = 1000) {
                let lastError = null;

                for (let attempt = 0; attempt <= maxRetries; attempt++) {
                    try {
                        console.log(`Attempting to fetch bias data (attempt ${attempt + 1}/${maxRetries + 1})`);

                        const response = await fetch(`/api/articles/${articleId}/bias`);
                        if (!response.ok) {
                            throw new Error(`Failed to fetch bias data: ${response.statusText}`);
                        }

                        const biasData = await response.json();
                        updateBiasUI(biasData);
                        console.log('Bias analysis updated successfully with fresh data');
                        return; // Success - exit the retry loop

                    } catch (error) {
                        lastError = error;
                        console.error(`Error updating bias analysis (attempt ${attempt + 1}):`, error);

                        // If this is not the last attempt, wait before retrying
                        if (attempt < maxRetries) {
                            const delay = baseDelay * Math.pow(2, attempt); // Exponential backoff
                            console.log(`Retrying in ${delay}ms...`);
                            await new Promise(resolve => setTimeout(resolve, delay));
                        }
                    }
                }

                // All retries failed - implement fallback behavior
                console.error('All retry attempts failed, implementing fallback behavior');
                await handleUpdateFailure(lastError, articleId);
            }

            // Update the UI elements with fresh bias data
            function updateBiasUI(biasData) {
                // Update bias score with the composite score
                if (biasScoreElement && biasData.composite_score !== null) {
                    biasScoreElement.textContent = `Bias Score: ${biasData.composite_score.toFixed(3)}`;
                } else if (biasScoreElement) {
                    biasScoreElement.textContent = 'Bias Score: Analysis pending';
                }

                // Update confidence value if available
                const confidenceElement = document.getElementById('confidence-value');
                if (confidenceElement && biasData.confidence !== null && biasData.confidence !== undefined) {
                    confidenceElement.textContent = biasData.confidence.toFixed(1);
                }

                // Update bias label if available
                const biasLabelContainer = document.getElementById('bias-label-container');
                if (biasLabelContainer && biasData.composite_score !== null) {
                    updateBiasLabel(biasLabelContainer, biasData.composite_score);
                }
            }

            // Update bias label based on score
            function updateBiasLabel(container, score) {
                let labelClass, labelText;

                if (score < -0.1) {
                    labelClass = 'bias-left';
                    labelText = `Left Leaning (${score.toFixed(3)})`;
                } else if (score > 0.1) {
                    labelClass = 'bias-right';
                    labelText = `Right Leaning (${score.toFixed(3)})`;
                } else {
                    labelClass = 'bias-center';
                    labelText = `Center (${score.toFixed(3)})`;
                }

                container.innerHTML = `<div class="bias-label ${labelClass}">${labelText}</div>`;
            }

            // Handle update failure with intelligent fallback options
            async function handleUpdateFailure(error, articleId) {
                console.log('Handling update failure with fallback options');

                // Check if this is a temporary network issue vs a permanent error
                const isTemporaryError = error.message.includes('Failed to fetch') ||
                                       error.message.includes('NetworkError') ||
                                       error.message.includes('timeout');

                if (isTemporaryError) {
                    // For temporary errors, offer automatic page refresh
                    console.log('Detected temporary error - offering automatic refresh');
                    await offerAutomaticRefresh();
                } else {
                    // For other errors, show the original fallback message
                    console.log('Detected permanent error - showing manual refresh message');
                    showManualRefreshMessage();
                }
            }

            // Offer automatic page refresh for temporary errors
            async function offerAutomaticRefresh() {
                if (biasScoreElement) {
                    biasScoreElement.textContent = 'Auto-refreshing content in 3 seconds...';
                    biasScoreElement.style.fontStyle = 'italic';
                    biasScoreElement.style.color = '#3b82f6'; // Blue color for info

                    // Countdown timer
                    for (let i = 3; i > 0; i--) {
                        biasScoreElement.textContent = `Auto-refreshing content in ${i} seconds...`;
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }

                    // Refresh the page
                    console.log('Auto-refreshing page to show updated results');
                    window.location.reload();
                }
            }

            // Show manual refresh message as fallback
            function showManualRefreshMessage() {
                if (biasScoreElement) {
                    const originalText = biasScoreElement.textContent;
                    biasScoreElement.textContent = 'Analysis complete - refresh page for updated results';
                    biasScoreElement.style.fontStyle = 'italic';
                    biasScoreElement.style.color = '#f59e0b'; // Amber color for warning

                    setTimeout(() => {
                        biasScoreElement.textContent = originalText;
                        biasScoreElement.style.fontStyle = 'normal';
                        biasScoreElement.style.color = '';
                    }, 5000);
                }
            }
        });
    </script>
</body>
</html>
