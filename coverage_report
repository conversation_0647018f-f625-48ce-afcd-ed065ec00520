mode: set
github.com/alexandru-savinov/BalancedNewsGo/cmd/clear_articles/main.go:12.18,14.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/clear_articles/main.go:14.16,16.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/clear_articles/main.go:17.2,17.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/clear_articles/main.go:17.15,17.33 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/clear_articles/main.go:19.2,20.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/clear_articles/main.go:20.16,22.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/clear_articles/main.go:24.2,25.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/clear_articles/main.go:25.16,27.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/clear_articles/main.go:29.2,30.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/clear_articles/main.go:33.13,34.30 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/clear_articles/main.go:34.30,37.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/delete_mock_scores/main.go:12.18,14.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/delete_mock_scores/main.go:14.16,16.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/delete_mock_scores/main.go:17.2,17.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/delete_mock_scores/main.go:17.15,17.33 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/delete_mock_scores/main.go:19.2,20.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/delete_mock_scores/main.go:20.16,22.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/delete_mock_scores/main.go:24.2,26.12 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/delete_mock_scores/main.go:29.13,30.30 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/delete_mock_scores/main.go:30.30,33.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:12.13,14.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:14.16,17.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:18.2,18.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:18.15,19.36 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:19.36,21.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:25.2,25.47 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:25.47,27.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:27.17,30.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:31.3,31.41 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:32.8,34.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:37.2,37.57 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:37.57,39.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:39.17,42.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:43.3,43.51 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:44.8,46.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:49.2,49.45 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:49.45,51.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:51.17,54.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:55.3,55.39 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:56.8,58.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:61.67,67.16 4 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:67.16,70.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/migrate_feedback_schema/main.go:71.2,71.18 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:14.52,16.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:16.16,18.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:19.2,19.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:19.15,20.43 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:20.43,22.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:25.2,26.65 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:26.65,28.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:30.2,30.20 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:30.20,32.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:34.2,35.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:35.16,37.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:38.2,38.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:38.15,39.38 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:39.38,41.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:44.2,49.25 4 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:49.25,51.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:52.2,52.45 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:52.45,54.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:57.2,57.27 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:57.27,59.28 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:59.28,62.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:63.3,63.46 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:63.46,65.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:68.2,68.12 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:71.13,82.44 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:82.44,84.58 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:84.58,86.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:86.9,88.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:92.2,93.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:93.16,96.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:97.2,97.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:97.15,98.43 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:98.43,100.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:103.2,104.66 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:104.66,107.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:109.2,109.29 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:109.29,112.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/generate_report/main.go:112.16,114.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/fetch_articles/main.go:17.18,18.40 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/fetch_articles/main.go:18.40,20.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/fetch_articles/main.go:21.2,22.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/fetch_articles/main.go:22.16,24.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/fetch_articles/main.go:25.2,25.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/fetch_articles/main.go:25.15,26.38 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/fetch_articles/main.go:26.38,28.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/fetch_articles/main.go:31.2,32.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/fetch_articles/main.go:32.16,34.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/fetch_articles/main.go:36.2,65.12 5 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/fetch_articles/main.go:68.13,69.30 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/fetch_articles/main.go:69.30,72.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:20.13,29.21 8 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:29.21,31.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:33.2,34.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:34.16,37.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:38.2,38.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:38.15,38.39 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:40.2,41.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:41.16,44.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:45.2,45.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:45.15,45.32 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:47.2,48.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:49.13,50.70 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:51.14,52.71 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:53.10,55.13 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:57.2,57.16 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:57.16,60.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:62.2,62.62 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:65.104,68.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:68.16,70.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:71.2,71.22 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:71.22,73.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:75.2,77.27 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:77.27,78.12 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:79.15,80.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:81.16,82.16 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:85.2,85.37 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:85.37,87.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:89.2,90.34 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:90.34,102.17 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:102.17,104.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:106.3,106.10 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:108.2,108.19 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:111.105,114.47 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:114.47,116.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:118.2,119.29 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:119.29,122.19 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:122.19,124.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:126.3,137.17 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:137.17,139.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:141.3,141.10 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/import_labels/main.go:143.2,143.19 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/reset_test_db/main.go:11.13,14.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/reset_test_db/main.go:14.16,16.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/reset_test_db/main.go:17.2,23.16 5 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/reset_test_db/main.go:23.16,25.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/reset_test_db/main.go:26.2,26.35 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/ingest_feedback/main.go:16.13,27.63 9 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/ingest_feedback/main.go:27.63,30.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/ingest_feedback/main.go:32.2,33.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/ingest_feedback/main.go:33.16,36.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/ingest_feedback/main.go:37.2,37.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/ingest_feedback/main.go:37.15,38.40 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/ingest_feedback/main.go:38.40,40.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/ingest_feedback/main.go:43.2,44.28 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/ingest_feedback/main.go:44.28,46.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/ingest_feedback/main.go:48.2,60.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/ingest_feedback/main.go:60.16,63.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/ingest_feedback/main.go:65.2,65.47 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:11.13,16.16 5 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:16.16,18.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:19.2,22.15 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:22.15,25.18 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:25.18,27.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:27.9,29.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:30.3,31.42 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:35.2,38.16 4 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:38.16,40.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:41.2,45.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:45.16,47.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:48.2,49.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:49.16,51.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:52.2,56.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:56.16,58.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:59.2,60.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:60.16,62.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:63.2,67.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:67.16,69.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:70.2,71.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:71.16,73.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/run_integration_setup_s3/main.go:74.2,76.111 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_article/main.go:20.13,31.16 7 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_article/main.go:31.16,34.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_article/main.go:35.2,35.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_article/main.go:35.15,36.36 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_article/main.go:36.36,38.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_article/main.go:41.2,43.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_article/main.go:43.16,46.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_article/main.go:48.2,49.68 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_scores/main.go:20.13,22.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_scores/main.go:22.16,25.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_scores/main.go:26.2,26.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_scores/main.go:26.15,27.36 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_scores/main.go:27.36,29.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_scores/main.go:32.2,34.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_scores/main.go:34.16,37.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_scores/main.go:39.2,39.27 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/query_scores/main.go:39.27,41.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:23.68,28.16 5 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:28.16,30.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:33.46,37.21 4 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:37.21,39.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:40.2,40.134 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:43.13,45.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:45.16,47.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:49.2,51.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:51.16,53.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:54.2,57.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:57.16,59.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:61.2,62.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:62.16,64.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:65.2,66.36 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:66.36,68.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:71.2,84.6 10 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:84.6,86.22 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:86.22,88.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:89.3,89.34 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:89.34,91.9 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:94.3,95.45 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:95.45,97.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:98.3,105.36 5 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:105.36,107.26 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:107.26,110.36 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:110.36,118.52 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:118.52,121.35 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:121.35,124.24 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:124.24,127.9 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:128.8,129.25 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:129.25,132.9 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:133.8,133.18 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:135.7,135.29 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:135.29,137.8 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:140.6,144.26 4 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:146.5,146.49 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:150.3,150.45 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:150.45,152.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:153.3,159.45 5 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:159.45,162.26 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:162.26,164.13 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:166.4,166.34 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:166.34,172.5 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:174.4,176.22 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:176.22,180.5 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:180.10,183.5 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:185.3,189.128 4 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/score_articles/main.go:195.2,199.53 5 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_llm/main.go:16.13,19.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_llm/main.go:19.16,21.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_llm/main.go:24.2,25.18 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_llm/main.go:25.18,27.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_llm/main.go:29.2,31.19 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_llm/main.go:31.19,33.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_llm/main.go:35.2,65.16 10 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_llm/main.go:65.16,67.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_llm/main.go:69.2,69.81 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:60.13,63.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:63.16,65.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:67.2,68.15 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:68.15,68.37 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:71.2,84.46 4 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:84.46,86.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:91.2,113.57 4 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:113.57,115.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:115.17,118.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:119.3,119.23 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:122.2,122.55 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:122.55,124.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:124.17,127.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:128.3,128.23 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:131.2,131.58 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:131.58,133.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:133.17,136.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:137.3,137.21 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:140.2,140.60 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:140.60,142.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:142.17,145.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:146.3,146.29 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:149.2,149.55 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:149.55,151.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:151.17,154.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:155.3,155.24 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:159.2,159.39 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:159.39,161.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:164.2,172.44 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:172.44,175.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:180.123,183.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:183.16,185.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:188.2,189.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:189.16,192.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:195.2,196.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:196.16,199.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:203.2,205.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:205.16,208.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:209.2,214.68 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:214.68,217.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:218.2,219.41 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:219.41,221.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:224.2,228.67 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:228.67,229.65 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:229.65,232.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:232.9,234.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:240.2,250.84 6 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:253.55,254.30 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:254.30,261.38 5 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:261.38,262.58 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:262.58,264.5 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:267.3,268.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:268.17,271.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:273.3,274.30 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:274.30,279.37 4 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:279.37,281.30 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:281.30,288.6 4 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:289.5,289.23 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:289.23,292.6 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:295.4,303.10 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:306.3,307.22 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:312.60,314.30 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/server/main.go:314.30,316.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_parse/main.go:12.13,14.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_parse/main.go:14.16,16.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_parse/main.go:17.2,20.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_parse/main.go:20.16,22.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_parse/main.go:24.2,24.37 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_parse/main.go:24.37,26.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_parse/main.go:28.2,30.34 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_parse/main.go:30.34,36.25 5 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_parse/main.go:36.25,38.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_parse/main.go:38.9,38.36 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/test_parse/main.go:38.36,40.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:52.13,70.2 10 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:72.64,74.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:74.16,76.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:77.2,78.25 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:81.48,84.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:84.16,86.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:87.2,87.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:90.106,101.31 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:101.31,103.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:103.17,105.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:108.3,111.18 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:111.18,113.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:115.3,119.34 4 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:119.34,121.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:123.3,125.18 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:128.2,128.30 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:132.80,134.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:134.16,136.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:137.2,139.22 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:143.60,145.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:145.16,147.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:151.49,157.64 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:157.64,160.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:161.2,161.41 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:165.72,166.28 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:166.28,169.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:170.2,172.13 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:176.117,187.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:190.75,191.53 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:191.53,192.67 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:192.67,194.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:198.39,205.42 5 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:205.42,207.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:210.93,211.46 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:211.46,212.37 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:212.37,217.4 4 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:219.2,219.19 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:222.76,223.9 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:224.58,225.25 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:225.25,227.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:227.9,230.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:231.58,232.23 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:233.58,234.23 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:236.2,236.8 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:239.43,250.2 9 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:252.54,254.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:256.60,258.36 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:258.36,260.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:261.2,264.34 4 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:264.34,266.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:267.2,267.52 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:270.59,271.21 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:271.21,273.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:274.2,276.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:276.16,279.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:280.2,280.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:280.15,281.35 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:281.35,283.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:286.2,289.16 4 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:289.16,291.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:294.41,295.19 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:295.19,297.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:297.8,297.25 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:297.25,299.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:300.2,300.21 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:303.42,304.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:305.36,306.19 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:307.36,308.20 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:309.40,310.22 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:311.10,312.19 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:316.35,319.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:319.16,322.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:323.2,323.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:323.15,324.35 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:324.35,326.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:329.2,332.16 4 0
github.com/alexandru-savinov/BalancedNewsGo/cmd/validate_labels/main.go:332.16,334.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/docs/docs.go:1206.13,1208.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/docs/docs.go:869.13,871.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:15.35,17.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:20.42,21.37 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:21.37,23.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:24.2,24.14 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:28.49,33.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:36.50,37.16 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:37.16,39.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:40.2,40.35 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:40.35,46.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:47.2,51.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:55.62,56.16 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:56.16,58.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:59.2,59.39 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:59.39,61.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:62.2,63.19 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:63.19,65.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:66.2,69.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:73.36,74.20 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:74.20,76.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:77.2,79.27 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:79.27,80.17 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:80.17,81.12 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:83.3,83.40 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:83.40,86.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:86.9,88.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:90.2,90.24 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:90.24,92.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/apperrors/errors.go:93.2,96.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:20.13,29.21 8 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:29.21,32.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:34.2,35.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:35.16,38.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:40.2,41.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:41.16,44.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:45.2,45.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:45.15,45.32 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:47.2,48.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:49.13,50.70 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:51.14,52.71 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:53.10,55.13 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:57.2,57.16 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:57.16,60.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:62.2,62.62 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:65.104,68.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:68.16,70.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:71.2,71.22 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:71.22,73.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:75.2,77.27 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:77.27,78.12 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:79.15,80.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:81.16,82.16 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:85.2,85.37 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:85.37,87.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:89.2,90.34 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:90.34,100.58 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:100.58,102.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:104.3,104.10 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:106.2,106.19 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:109.105,112.47 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:112.47,114.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:116.2,117.29 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:117.29,120.19 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:120.19,122.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:124.3,133.58 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:133.58,135.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:137.3,137.10 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/import_labels/main.go:139.2,139.19 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/metrics/metrics.go:41.68,45.2 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/metrics/metrics.go:47.65,51.2 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/metrics/metrics.go:53.66,57.2 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/metrics/metrics.go:59.60,63.2 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/metrics/metrics.go:65.60,69.2 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/metrics/prom.go:33.23,37.2 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/metrics/prom.go:39.46,41.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/metrics/prom.go:43.59,45.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/metrics/prom.go:47.60,49.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:27.60,31.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:34.54,38.2 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:41.45,46.34 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:46.34,47.21 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:47.21,49.25 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:49.25,55.5 5 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:56.9,59.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:62.2,65.35 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:68.60,75.60 5 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:75.60,77.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:79.2,79.12 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:82.75,86.2 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:94.50,96.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:99.64,102.33 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:102.33,104.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:104.17,106.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:107.3,107.54 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/coordinator.go:111.2,112.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:13.57,14.30 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:14.30,16.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:18.2,19.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:19.16,21.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:22.2,33.40 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:33.40,34.44 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:34.44,36.18 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:36.18,38.5 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:42.2,42.36 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:42.36,44.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:45.2,45.12 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:49.75,63.16 6 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:63.16,65.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:66.2,66.23 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:71.84,80.33 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:80.33,90.17 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:90.17,92.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:94.2,94.12 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:99.85,111.33 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:111.33,121.17 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:121.17,123.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:125.2,125.12 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:129.74,140.37 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:140.37,151.17 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:151.17,153.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/testing/integration_helpers.go:155.2,155.12 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/balancer/balancer.go:8.69,12.31 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/balancer/balancer.go:12.31,15.3 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/balancer/balancer.go:18.2,19.31 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/balancer/balancer.go:19.31,21.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/balancer/balancer.go:23.2,31.15 6 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:45.64,51.2 4 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:53.57,56.41 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:56.41,58.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:59.2,59.12 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:71.3,211.2 13 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:214.59,215.30 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:215.30,216.16 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:216.16,217.32 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:217.32,222.5 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:224.3,224.13 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:229.67,239.2 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:254.60,255.30 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:255.30,265.46 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:265.46,266.54 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:266.54,269.5 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:270.4,271.10 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:275.3,276.23 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:276.23,278.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:279.3,279.20 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:279.20,281.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:282.3,282.22 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:282.22,284.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:285.3,285.24 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:285.24,287.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:288.3,288.24 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:288.24,290.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:292.3,292.29 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:292.29,296.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:299.3,299.88 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:299.88,302.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:305.3,306.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:306.17,309.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:310.3,310.13 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:310.13,319.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:322.3,323.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:323.17,326.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:328.3,343.17 5 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:343.17,344.42 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:344.42,353.5 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:354.4,355.10 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:359.3,360.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:360.17,363.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:365.3,368.5 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:389.58,390.30 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:390.30,398.45 7 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:398.45,402.4 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:403.3,404.31 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:404.31,408.4 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:410.3,412.17 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:412.17,416.4 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:419.3,419.27 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:419.27,421.23 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:421.23,423.30 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:423.30,430.6 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:431.5,431.23 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:431.23,436.6 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:441.3,442.27 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:442.27,444.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:446.3,450.46 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:467.61,468.30 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:468.30,473.27 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:473.27,476.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:479.3,481.58 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:481.58,486.4 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:487.3,490.17 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:490.17,491.45 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:491.45,494.5 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:495.4,497.10 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:501.3,512.49 6 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:525.74,526.30 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:526.30,531.3 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:547.114,548.30 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:548.30,551.27 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:551.27,554.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:555.3,560.17 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:560.17,561.45 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:561.45,564.5 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:565.4,566.10 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:570.3,571.48 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:571.48,574.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:577.3,577.51 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:577.51,579.32 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:580.17,581.19 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:582.17,583.28 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:584.13,585.28 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:586.15,587.28 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:588.16,591.24 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:591.24,595.6 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:596.12,599.11 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:602.4,602.45 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:602.45,606.5 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:608.4,610.18 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:610.18,614.5 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:617.4,617.27 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:617.27,619.5 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:621.4,626.10 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:630.3,631.44 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:631.44,634.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:637.3,640.45 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:640.45,642.27 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:642.27,644.5 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:644.10,648.5 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:649.9,653.43 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:653.43,656.25 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:656.25,658.11 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:661.5,661.61 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:661.61,662.11 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:665.4,665.48 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:668.3,668.25 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:668.25,672.4 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:675.3,675.26 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:675.26,683.46 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:683.46,684.15 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:684.15,686.20 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:686.20,694.7 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:695.6,699.8 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:701.10,709.5 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:712.3,715.5 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:729.48,730.30 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:730.30,733.27 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:733.27,736.4 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:737.3,749.7 10 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:749.7,750.11 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:751.38,752.11 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:753.20,755.24 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:755.24,756.14 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:760.5,760.56 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:760.56,762.41 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:762.41,763.76 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:763.76,766.8 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:767.7,771.69 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:771.69,773.8 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:782.35,783.16 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:783.16,785.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:786.2,787.13 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:787.13,789.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:790.2,790.10 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:802.77,803.30 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:803.30,806.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:821.60,823.2 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:825.49,830.26 4 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:830.26,833.3 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:836.2,838.57 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:838.57,843.3 4 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:844.2,848.16 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:848.16,849.44 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:849.44,852.4 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:853.3,854.9 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:857.2,858.16 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:858.16,861.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:863.2,863.31 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:863.31,864.37 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:864.37,868.72 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:868.72,869.46 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:869.46,871.6 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:873.4,883.10 7 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:887.2,888.41 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:913.51,914.30 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:914.30,918.34 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:918.34,922.4 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:924.3,925.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:925.17,929.4 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:930.3,931.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:931.17,935.4 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:936.3,937.52 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:937.52,941.4 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:944.3,946.58 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:946.58,951.4 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:952.3,955.17 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:955.17,959.4 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:961.3,965.25 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:965.25,968.36 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:968.36,969.91 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:969.91,971.6 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:972.10,982.29 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:982.29,983.74 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:983.74,984.33 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:984.33,986.8 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:987.7,987.34 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:987.34,989.8 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:990.12,992.7 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:993.11,995.6 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:998.5,998.59 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:998.59,1006.6 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1011.3,1011.59 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1011.59,1015.20 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1015.20,1018.5 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1019.4,1019.12 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1019.12,1022.5 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1023.4,1023.12 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1023.12,1026.5 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1029.4,1029.28 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1029.28,1031.5 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1032.4,1032.26 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1035.3,1037.33 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1037.33,1039.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1039.9,1042.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1044.3,1049.19 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1049.19,1051.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1054.3,1059.24 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1059.24,1061.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1063.3,1064.39 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1076.62,1077.30 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1077.30,1081.27 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1081.27,1085.4 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1088.3,1088.50 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1088.50,1091.18 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1091.18,1095.5 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1096.4,1097.25 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1097.25,1100.5 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1101.4,1103.10 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1107.3,1109.61 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1109.61,1117.4 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1118.3,1121.17 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1121.17,1125.4 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1127.3,1128.24 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1128.24,1132.4 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1134.3,1142.50 5 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1147.75,1149.31 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1149.31,1150.35 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1150.35,1151.12 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1154.3,1155.71 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1155.71,1164.12 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1167.3,1168.11 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1168.11,1170.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1172.3,1173.11 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1173.11,1175.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1177.3,1182.5 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1184.2,1184.16 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1198.81,1199.30 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1199.30,1210.44 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1210.44,1213.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1216.3,1217.25 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1217.25,1219.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1220.3,1220.29 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1220.29,1222.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1223.3,1223.23 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1223.23,1225.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1227.3,1227.29 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1227.29,1231.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1234.3,1241.59 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1241.59,1244.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1246.3,1258.17 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1258.17,1261.4 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1264.3,1265.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1265.17,1268.21 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1268.21,1272.5 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1274.4,1275.18 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1275.18,1277.5 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1277.10,1279.32 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1279.32,1281.6 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1281.11,1281.42 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1281.42,1283.6 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1286.5,1287.19 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1287.19,1290.6 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1294.3,1295.43 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1308.58,1309.30 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1309.30,1312.27 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1312.27,1316.4 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1317.3,1323.46 5 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1323.46,1327.4 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1329.3,1329.43 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1329.43,1333.4 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1335.3,1336.10 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1336.10,1338.50 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1338.50,1340.5 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1340.10,1344.5 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1346.3,1346.40 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1346.40,1350.4 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1353.3,1354.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1354.17,1355.45 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1355.45,1358.5 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1359.4,1361.10 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1365.3,1366.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1366.17,1375.53 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1375.53,1379.5 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1380.4,1383.10 4 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1385.3,1390.5 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/api.go:1395.31,1397.2 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/cache.go:19.36,23.2 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/cache.go:25.59,30.13 4 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/cache.go:30.13,32.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/cache.go:34.2,34.40 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/cache.go:34.40,37.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/cache.go:39.2,39.26 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/cache.go:42.77,50.2 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/cache.go:52.42,56.2 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/errors.go:124.60,129.2 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/errors.go:132.76,133.16 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/errors.go:133.16,135.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/errors.go:136.2,137.19 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/errors.go:137.19,139.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/errors.go:140.2,140.35 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/response.go:13.55,18.2 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/response.go:21.60,22.16 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/response.go:22.16,24.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/response.go:27.2,34.4 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/response.go:38.44,39.16 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/response.go:39.16,40.50 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/response.go:40.50,43.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/response.go:43.9,46.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/response.go:51.56,54.2 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/response.go:57.37,58.14 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/response.go:59.21,60.31 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/response.go:61.19,62.29 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/response.go:63.20,64.36 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/response.go:65.21,66.39 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/api/response.go:67.19,68.29 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/api/response.go:69.10,70.40 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/tests/unit/test_utils.go:18.46,29.2 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/tests/unit/test_utils.go:32.70,38.2 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/tests/unit/test_utils.go:41.84,44.50 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/tests/unit/test_utils.go:44.50,47.3 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/tests/unit/test_utils.go:49.2,49.15 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/tests/unit/test_utils.go:53.58,55.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:22.47,30.27 4 1
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:30.27,31.35 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:31.35,33.4 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:35.2,35.14 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:46.88,53.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:56.38,57.48 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:57.48,60.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:61.2,61.16 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:61.16,65.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:67.2,68.67 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:72.37,75.2 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:78.37,81.37 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:81.37,83.18 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:83.18,84.12 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:87.3,87.35 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:87.35,89.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:93.75,94.28 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:94.28,96.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:98.2,99.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:99.16,102.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:103.2,103.9 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:103.9,105.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:108.2,109.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:109.16,112.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:113.2,113.17 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:113.17,116.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:118.2,120.48 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:120.48,123.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:126.83,129.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:129.16,132.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:133.2,133.13 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:136.60,137.24 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:137.24,140.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:141.2,141.14 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:144.66,146.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:146.16,148.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:149.2,149.12 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:149.12,152.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:154.2,154.19 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:157.62,158.24 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:158.24,160.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:161.2,161.47 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:161.47,162.60 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:162.60,163.30 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:163.30,165.5 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:168.2,168.28 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:168.28,170.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:171.2,171.11 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:174.85,176.33 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:176.33,178.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:180.2,186.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:189.61,191.19 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:191.19,193.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:195.2,196.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:196.16,198.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:200.2,201.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:205.42,206.17 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:206.17,208.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:210.2,210.41 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:210.41,212.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:215.2,216.24 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:216.24,218.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:218.8,218.54 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:218.54,219.60 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:219.60,220.30 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:220.30,222.5 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:224.8,224.35 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:224.35,226.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:227.2,227.38 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:227.38,229.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:231.2,231.13 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:235.55,239.37 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:239.37,241.17 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:241.17,244.12 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:246.3,247.47 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/rss/rss.go:249.2,249.16 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:132.86,134.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:137.88,139.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:142.97,144.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:144.16,146.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:149.2,150.26 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:150.26,152.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:153.2,153.20 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:157.120,159.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:159.16,161.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:164.2,165.26 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:165.26,167.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:168.2,168.20 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:172.90,174.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:177.120,179.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:182.129,183.18 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:183.18,185.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:186.2,186.69 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:190.88,192.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:195.96,196.21 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:196.21,198.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:199.2,208.41 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:212.84,214.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:217.95,219.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:222.123,226.2 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:229.50,231.16 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:231.16,233.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:234.2,234.33 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:238.36,240.2 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:243.47,244.16 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:244.16,246.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:248.2,249.9 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:250.28,251.41 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:252.102,253.25 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:254.58,255.53 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:256.10,257.40 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:262.51,267.16 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:267.16,269.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:271.2,272.16 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:272.16,274.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:275.2,276.12 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:280.60,285.16 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:285.16,287.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:289.2,290.16 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:290.16,292.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:293.2,294.12 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:298.78,305.16 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:305.16,306.27 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:306.27,308.4 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:309.3,309.72 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:311.2,311.19 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:315.75,320.16 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:320.16,321.27 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:321.27,323.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:324.3,324.74 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:326.2,326.24 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:330.75,333.74 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:333.74,335.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:337.2,345.16 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:345.16,347.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:348.2,348.20 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:352.66,354.16 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:354.16,356.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:358.2,358.15 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:358.15,359.31 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:359.31,361.12 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:362.9,362.24 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:362.24,364.4 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:368.2,368.32 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:368.32,370.3 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:371.2,371.27 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:371.27,374.3 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:375.2,375.30 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:375.30,378.3 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:379.2,379.30 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:379.30,382.3 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:385.2,387.40 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:387.40,389.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:390.2,390.12 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:390.12,393.3 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:396.2,402.16 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:402.16,406.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:408.2,409.16 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:409.16,412.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:415.2,416.16 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:416.16,418.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:420.2,421.16 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:425.75,430.16 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:430.16,432.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:433.2,433.30 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:437.106,441.18 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:441.18,444.3 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:445.2,445.19 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:445.19,446.18 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:447.15,448.42 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:449.16,450.41 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:451.17,452.56 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:456.2,466.16 7 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:466.16,469.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:471.2,472.22 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:476.64,478.15 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:478.15,481.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:483.2,490.52 5 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:490.52,493.17 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:493.17,497.4 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:499.3,499.27 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:499.27,503.9 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:506.3,508.29 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:508.29,511.4 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:515.2,515.26 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:515.26,518.3 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:519.2,520.57 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:524.71,527.16 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:527.16,529.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:530.2,530.20 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:534.96,540.16 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:540.16,542.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:543.2,543.12 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:547.108,557.16 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:557.16,560.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:562.2,563.16 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:563.16,565.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:565.8,569.24 2 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:569.24,571.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:574.2,574.12 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:578.64,581.16 3 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:581.16,583.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:584.2,584.20 1 1
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:588.46,591.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:591.16,593.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:596.2,596.33 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:596.33,597.46 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:597.46,599.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:600.3,600.61 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:604.2,610.16 5 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:610.16,613.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:632.2,687.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:687.16,689.46 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:689.46,691.4 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:692.3,692.18 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:696.2,696.16 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:700.86,703.16 3 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:703.16,706.3 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:708.2,709.16 2 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:709.16,712.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:712.8,712.30 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:712.30,716.3 1 0
github.com/alexandru-savinov/BalancedNewsGo/internal/db/db.go:718.2,719.12 2 0
