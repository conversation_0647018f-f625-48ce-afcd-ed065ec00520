import { test, expect } from '@playwright/test';

/**
 * Simple test for Issue #93: Auto-refresh after reanalysis completion
 * 
 * This test validates the simple retry + auto-refresh approach
 */

test.describe('Simple Auto-refresh Fix', () => {
  const ARTICLE_URL = '/article/5';
  const TEST_TIMEOUT = 90000; // 90 seconds

  test('should auto-refresh or retry when reanalysis completes', async ({ page }) => {
    test.setTimeout(TEST_TIMEOUT);
    
    console.log('🧪 Testing simple auto-refresh fix');

    // Navigate to article
    await page.goto(ARTICLE_URL);
    await page.waitForLoadState('load');
    console.log('✅ Page loaded');

    // Get initial state
    const initialBiasScore = await page.locator('#bias-score').textContent();
    console.log('📊 Initial bias score:', initialBiasScore);

    // Click reanalysis button
    const reanalyzeBtn = page.locator('#reanalyze-btn');
    await expect(reanalyzeBtn).toBeVisible();
    await expect(reanalyzeBtn).toBeEnabled();
    
    console.log('🖱️ Clicking reanalysis button');
    await reanalyzeBtn.click();

    // Wait for button to be disabled
    await expect(reanalyzeBtn).toBeDisabled({ timeout: 5000 });
    console.log('✅ Button disabled - reanalysis started');

    // Wait for completion (button re-enabled)
    console.log('⏳ Waiting for reanalysis completion...');
    await expect(reanalyzeBtn).toBeEnabled({ timeout: 60000 });
    console.log('✅ Button re-enabled - reanalysis completed');

    // Wait a moment for updateBiasAnalysis to run
    await page.waitForTimeout(8000); // 4s delay + some buffer

    // Check final state
    const finalBiasScore = await page.locator('#bias-score').textContent();
    console.log('📊 Final bias score:', finalBiasScore);

    // Key assertion: NO manual refresh message should be shown
    expect(finalBiasScore).not.toContain('Analysis complete - refresh page for updated results');
    console.log('✅ NO manual refresh message detected');

    // Should either show updated bias score OR auto-refresh message
    const hasUpdatedScore = finalBiasScore?.includes('Bias Score:');
    const hasAutoRefreshMessage = finalBiasScore?.includes('Auto-refreshing page in');
    
    if (hasUpdatedScore) {
      console.log('✅ Bias score updated successfully');
      expect(finalBiasScore).toMatch(/Bias Score: -?\d+\.\d{3}/);
    } else if (hasAutoRefreshMessage) {
      console.log('✅ Auto-refresh countdown detected - waiting for page refresh');
      
      // Wait for page to refresh
      await page.waitForLoadState('load', { timeout: 10000 });
      console.log('✅ Page auto-refreshed successfully');
      
      // Check bias score after refresh
      const refreshedBiasScore = await page.locator('#bias-score').textContent();
      console.log('📊 Bias score after refresh:', refreshedBiasScore);
      
    } else {
      console.log('ℹ️ Unexpected final state - but no manual refresh message shown');
    }

    console.log('🎉 Simple auto-refresh test completed successfully');
  });
});
