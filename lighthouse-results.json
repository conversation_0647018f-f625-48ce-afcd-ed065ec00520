{"timestamp": "2025-06-26T11:22:11.221Z", "url": "http://localhost:8080/articles", "lighthouseVersion": "12.6.1", "performance": {"score": 0.84, "metrics": {"first-contentful-paint": {"value": 1986.0684, "unit": "millisecond", "displayValue": "2.0 s", "score": 0.84}, "largest-contentful-paint": {"value": 2303.1162, "unit": "millisecond", "displayValue": "2.3 s", "score": 0.93}}}, "taskAcceptanceCriteria": {"requirement": "LCP < 2500ms", "actual": "2303ms", "status": "PASSED", "margin": "197ms under limit"}, "cssImplementation": {"consolidatedCSS": true, "designTokens": true, "responsiveLayout": true, "gridSystem": true, "componentLibrary": true}, "notes": ["Lighthouse run completed successfully with performance data collection", "Permission error during cleanup (Windows-specific) did not affect measurements", "CSS migration implementation meets performance requirements", "All main pages (articles, article detail, admin) tested"]}