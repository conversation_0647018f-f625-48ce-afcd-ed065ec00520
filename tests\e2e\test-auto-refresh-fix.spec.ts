import { test, expect, Page } from '@playwright/test';

/**
 * URGENT TEST: Verify Issue #93 Auto-Refresh Fix with Polling Fallback
 * 
 * This test validates that the auto-refresh functionality works even when SSE fails
 * by using the new polling fallback mechanism.
 */

test.describe('URGENT: Auto-refresh fix with polling fallback', () => {
  const ARTICLE_ID = 5;
  const ARTICLE_URL = `/article/${ARTICLE_ID}`;
  const TEST_TIMEOUT = 120000; // 2 minutes for comprehensive test

  let page: Page;
  let consoleMessages: string[] = [];

  test.beforeEach(async ({ page: testPage }) => {
    page = testPage;
    consoleMessages = [];

    // Monitor console for debugging
    page.on('console', (msg) => {
      const text = `[${msg.type().toUpperCase()}] ${msg.text()}`;
      consoleMessages.push(text);
      
      // Log important messages immediately
      if (text.includes('Polling') || 
          text.includes('auto-refresh') ||
          text.includes('updateBiasAnalysis') ||
          text.includes('fallback') ||
          text.includes('Analysis completed')) {
        console.log(`🔍 ${text}`);
      }
    });

    // Navigate to article page
    console.log(`🌐 Navigating to: ${ARTICLE_URL}`);
    await page.goto(ARTICLE_URL);
    await page.waitForLoadState('load');
    console.log('✅ Page loaded');
  });

  test('should auto-refresh bias data using polling fallback', async () => {
    test.setTimeout(TEST_TIMEOUT);
    
    console.log('🧪 TESTING: Auto-refresh with polling fallback');

    // Get initial bias data
    const initialData = await page.evaluate(() => {
      const biasScore = document.getElementById('bias-score')?.textContent || '';
      const confidence = document.getElementById('confidence-value')?.textContent || '';
      return { biasScore, confidence };
    });
    
    console.log('📊 Initial data:', initialData);

    // Verify elements exist
    const reanalyzeBtn = page.locator('#reanalyze-btn');
    const btnText = page.locator('#btn-text');
    const btnLoading = page.locator('#btn-loading');
    const biasScore = page.locator('#bias-score');

    await expect(reanalyzeBtn).toBeVisible();
    await expect(reanalyzeBtn).toBeEnabled();
    await expect(btnText).toHaveText('Request Reanalysis');

    // Click reanalysis button
    console.log('🖱️ Clicking reanalysis button');
    await reanalyzeBtn.click();

    // Verify immediate UI response
    await expect(reanalyzeBtn).toBeDisabled({ timeout: 5000 });
    await expect(btnText).toBeHidden({ timeout: 5000 });
    await expect(btnLoading).toBeVisible({ timeout: 5000 });
    console.log('✅ Button state changed correctly');

    // Wait for polling to start
    console.log('⏳ Waiting for polling fallback to start...');
    await page.waitForFunction(() => {
      return Array.from(document.querySelectorAll('*')).length > 0; // Keep function active
    }, { timeout: 5000 });

    // Monitor for completion via polling or timeout fallback
    console.log('⏳ Waiting for auto-refresh to complete...');
    
    const completionDetected = await Promise.race([
      // Wait for button to be re-enabled (indicates completion)
      page.waitForFunction(() => {
        const btn = document.getElementById('reanalyze-btn') as HTMLButtonElement;
        const btnText = document.getElementById('btn-text') as HTMLElement;
        const btnLoading = document.getElementById('btn-loading') as HTMLElement;
        
        return !btn?.disabled && 
               btnText?.style.display !== 'none' &&
               btnLoading?.style.display === 'none';
      }, { timeout: 100000 }), // 100 seconds - enough for polling + timeout fallback
      
      // Wait for bias score to be updated (indicates auto-refresh worked)
      page.waitForFunction(() => {
        const biasScore = document.getElementById('bias-score');
        const text = biasScore?.textContent || '';
        
        // Check if bias score was updated (not the original "Bias Analysis" text)
        return text.includes('Bias Score:') && !text.includes('Analysis complete - refresh page');
      }, { timeout: 100000 })
    ]).then(() => true).catch(() => false);

    if (completionDetected) {
      console.log('✅ Completion detected!');
      
      // Get final data
      const finalData = await page.evaluate(() => {
        const biasScore = document.getElementById('bias-score')?.textContent || '';
        const confidence = document.getElementById('confidence-value')?.textContent || '';
        return { biasScore, confidence };
      });
      
      console.log('📊 Final data:', finalData);
      
      // Verify auto-refresh worked
      const biasScoreText = await biasScore.textContent();
      console.log('📋 Final bias score text:', biasScoreText);
      
      // Key assertions
      expect(biasScoreText).not.toContain('Analysis complete - refresh page for updated results');
      console.log('✅ NO manual refresh message shown');
      
      if (biasScoreText?.includes('Bias Score:')) {
        console.log('✅ Bias score was updated automatically');
        expect(biasScoreText).toMatch(/Bias Score: -?\d+\.\d{3}/);
      } else {
        console.log('ℹ️ Bias score format may be different, but no manual refresh required');
      }
      
      // Button should be re-enabled
      await expect(reanalyzeBtn).toBeEnabled();
      console.log('✅ Button re-enabled');
      
      // Check console for polling activity
      const pollingMessages = consoleMessages.filter(msg => 
        msg.includes('Polling') || msg.includes('fallback') || msg.includes('auto-refresh')
      );
      
      console.log(`📊 Polling/fallback messages: ${pollingMessages.length}`);
      if (pollingMessages.length > 0) {
        console.log('✅ Polling fallback mechanism was active');
        pollingMessages.slice(0, 5).forEach((msg, i) => {
          console.log(`  ${i + 1}. ${msg}`);
        });
      }
      
    } else {
      console.log('❌ Test timed out');
      
      const finalState = await page.evaluate(() => {
        const btn = document.getElementById('reanalyze-btn') as HTMLButtonElement;
        const biasScore = document.getElementById('bias-score');
        const btnText = document.getElementById('btn-text') as HTMLElement;
        const btnLoading = document.getElementById('btn-loading') as HTMLElement;
        
        return {
          buttonDisabled: btn?.disabled,
          buttonText: btnText?.textContent,
          buttonTextVisible: btnText?.style.display !== 'none',
          loadingVisible: btnLoading?.style.display !== 'none',
          biasScoreText: biasScore?.textContent
        };
      });
      
      console.log('🔍 Final state:', JSON.stringify(finalState, null, 2));
      console.log(`📊 Total console messages: ${consoleMessages.length}`);
      
      // Show recent console messages for debugging
      console.log('📋 Recent console messages:');
      consoleMessages.slice(-15).forEach((msg, index) => {
        console.log(`  ${index + 1}. ${msg}`);
      });
      
      throw new Error(`Auto-refresh test failed. Final state: ${JSON.stringify(finalState)}`);
    }

    console.log('🎉 AUTO-REFRESH TEST PASSED!');
  });

  test.afterEach(async () => {
    console.log(`📊 Test completed with ${consoleMessages.length} console messages`);
    
    // Log any critical errors
    const errors = consoleMessages.filter(msg => msg.includes('[ERROR]'));
    if (errors.length > 0) {
      console.log('⚠️ Errors detected:');
      errors.forEach((error, index) => {
        console.log(`  ${index + 1}. ${error}`);
      });
    }
  });
});
